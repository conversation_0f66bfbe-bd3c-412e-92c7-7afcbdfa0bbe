// Database-backed token storage with fallback to in-memory for development
import prisma from "../db.server.js";

// Fallback in-memory store for development/testing
const tokenStore = new Map();

export const saveToken = async (shopUrl, token) => {
  try {
    // Try database first
    if (prisma) {
      await prisma.apiToken.upsert({
        where: { shop: shopUrl },
        update: {
          token,
          updatedAt: new Date(),
          isActive: true
        },
        create: {
          shop: shopUrl,
          token,
          isActive: true
        },
      });
      console.log(`✅ Token saved to database for ${shopUrl}`);
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Database save failed, using in-memory fallback:', error.message);
  }

  // Fallback to in-memory storage
  try {
    tokenStore.set(shopUrl, token);
    console.log(`✅ Token saved to memory for ${shopUrl}`);
    return true;
  } catch (error) {
    console.error('❌ Error saving token:', error);
    return false;
  }
};

export const getToken = async (shopUrl) => {
  try {
    // Try database first
    if (prisma) {
      const record = await prisma.apiToken.findUnique({
        where: { shop: shopUrl, isActive: true },
      });
      if (record) {
        console.log(`🔍 Token retrieved from database for ${shopUrl}`);
        return record.token;
      }
    }
  } catch (error) {
    console.warn('⚠️ Database read failed, using in-memory fallback:', error.message);
  }

  // Fallback to in-memory storage
  try {
    const token = tokenStore.get(shopUrl);
    if (token) {
      console.log(`🔍 Token retrieved from memory for ${shopUrl}`);
    }
    return token || null;
  } catch (error) {
    console.error('❌ Error retrieving token:', error);
    return null;
  }
};

export const getAllTokens = async () => {
  try {
    // Try database first
    if (prisma) {
      const records = await prisma.apiToken.findMany({
        where: { isActive: true },
      });
      console.log('📋 All stored tokens from database');
      return records.reduce((acc, record) => {
        acc[record.shop] = record.token;
        return acc;
      }, {});
    }
  } catch (error) {
    console.warn('⚠️ Database read failed, using in-memory fallback:', error.message);
  }

  // Fallback to in-memory storage
  console.log('📋 All stored tokens from memory:');
  tokenStore.forEach((token, shop) => {
    console.log(`  ${shop}: ${token}`);
  });
  return Object.fromEntries(tokenStore);
};

export const deleteToken = async (shopUrl) => {
  try {
    // Try database first
    if (prisma) {
      await prisma.apiToken.update({
        where: { shop: shopUrl },
        data: { isActive: false },
      });
      console.log(`🗑️ Token deactivated in database for ${shopUrl}`);
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Database delete failed, using in-memory fallback:', error.message);
  }

  // Fallback to in-memory storage
  try {
    const result = tokenStore.delete(shopUrl);
    console.log(`🗑️ Token deleted from memory for ${shopUrl}:`, result);
    return result;
  } catch (error) {
    console.error('❌ Error deleting token:', error);
    return false;
  }
};