// Centralized configuration management
export const config = {
  // External API Configuration
  rushrr: {
    baseUrl: process.env.RUSHRR_API_BASE_URL || 'https://backend.rushr-admin.com/api',
    endpoints: {
      orders: '/orders',
      book: '/orders/book',
      update: '/orders/update',
      auth: {
        verify: '/auth/verify-shopify-store',
        verifyApiKey: '/auth/verify-api-key',
      },
    },
  },

  // App Configuration
  app: {
    name: 'Rushrr Courier',
    version: '1.0.0',
    defaultWeight: '0.5', // kg
    supportEmail: '<EMAIL>',
    supportPhone: '+92-XXX-XXXXXXX',
  },

  // Development Configuration
  dev: {
    logLevel: process.env.LOG_LEVEL || 'info',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  },

  // Security Configuration
  security: {
    sessionSecret: process.env.SESSION_SECRET || 'development_session_secret',
    tokenExpiry: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  },

  // Database Configuration
  database: {
    url: process.env.DATABASE_URL || 'file:dev.sqlite',
  },
};

// Helper functions
export const getApiUrl = (endpoint) => {
  const baseUrl = config.rushrr.baseUrl;
  const endpointPath = config.rushrr.endpoints[endpoint] || endpoint;
  return `${baseUrl}${endpointPath}`;
};

export const getRushrApiUrl = (path) => {
  return `${config.rushrr.baseUrl}${path}`;
};

// Validation helper
export const validateConfig = () => {
  const warnings = [];
  
  if (!process.env.SHOPIFY_API_KEY) {
    warnings.push('SHOPIFY_API_KEY not set');
  }
  
  if (!process.env.SHOPIFY_API_SECRET) {
    warnings.push('SHOPIFY_API_SECRET not set');
  }
  
  if (!process.env.SHOPIFY_APP_URL) {
    warnings.push('SHOPIFY_APP_URL not set');
  }
  
  if (warnings.length > 0) {
    console.warn('⚠️ Configuration warnings:', warnings.join(', '));
  }
  
  return warnings.length === 0;
};

export default config;
