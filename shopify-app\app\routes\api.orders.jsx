// routes/api/orders.jsx
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { getToken } from "../utils/tokenStorage";
import { getRushrApiUrl } from "../utils/config";

export async function loader({ request }) {
  try {
    // 🔒 Authenticate the session
    const { session } = await authenticate.admin(request);
    const shop = session.shop;

    // 🔑 Get token for current shop from database/memory store
    const token = await getToken(shop);

    if (!token) {
      console.warn(`❌ No token found for shop: ${shop}`);
      return json({
        success: false,
        error: "Token not found for this store. Please configure your API token in settings."
      }, { status: 401 });
    }

    // 📨 Fetch orders from external API using centralized config
    const apiUrl = getRushrApiUrl('/orders');
    const res = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      console.error(`❌ API request failed: ${res.status} ${res.statusText}`);
      return json({
        success: false,
        error: `Failed to fetch orders: ${res.statusText}`
      }, { status: res.status });
    }

    const data = await res.json();

    return json({
      success: true,
      orders: data.orders || [],
      message: `Fetched ${(data.orders || []).length} orders successfully`
    });
  } catch (err) {
    console.error("❌ Error fetching orders:", err);
    return json({
      success: false,
      error: "Failed to fetch orders. Please check your connection and try again."
    }, { status: 500 });
  }
}
