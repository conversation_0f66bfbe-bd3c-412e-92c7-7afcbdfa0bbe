// Input validation utilities for forms and API requests

import { ValidationError } from './errorHandler.js';

// Order validation schema
export const validateOrderData = (orderData) => {
  const errors = [];

  // Required fields
  if (!orderData.customer_name?.trim()) {
    errors.push({ field: 'customer_name', message: 'Customer name is required' });
  }

  if (!orderData.customer_phone?.trim()) {
    errors.push({ field: 'customer_phone', message: 'Customer phone is required' });
  } else if (!isValidPhone(orderData.customer_phone)) {
    errors.push({ field: 'customer_phone', message: 'Invalid phone number format' });
  }

  if (!orderData.customer_address?.trim()) {
    errors.push({ field: 'customer_address', message: 'Customer address is required' });
  }

  if (!orderData.destination_city?.trim()) {
    errors.push({ field: 'destination_city', message: 'Destination city is required' });
  }

  if (!orderData.weight) {
    errors.push({ field: 'weight', message: 'Weight is required' });
  } else {
    const weight = parseFloat(orderData.weight);
    if (isNaN(weight) || weight <= 0) {
      errors.push({ field: 'weight', message: 'Weight must be a positive number' });
    } else if (weight > 1000) {
      errors.push({ field: 'weight', message: 'Weight cannot exceed 1000 kg' });
    }
  }

  // Optional but validated fields
  if (orderData.customer_email && !isValidEmail(orderData.customer_email)) {
    errors.push({ field: 'customer_email', message: 'Invalid email format' });
  }

  if (orderData.cod_amount) {
    const codAmount = parseFloat(orderData.cod_amount);
    if (isNaN(codAmount) || codAmount < 0) {
      errors.push({ field: 'cod_amount', message: 'COD amount must be a valid positive number' });
    }
  }

  return errors;
};

// Individual validation functions
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhone = (phone) => {
  // Remove common formatting characters
  const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, '');
  // Check if it's a valid Pakistani phone number or international format
  const phoneRegex = /^(\+92|92|0)?[0-9]{10,11}$/;
  return phoneRegex.test(cleanPhone);
};

export const isValidWeight = (weight) => {
  const weightNum = parseFloat(weight);
  return !isNaN(weightNum) && weightNum > 0 && weightNum <= 1000;
};

export const isValidCODAmount = (amount) => {
  const amountNum = parseFloat(amount);
  return !isNaN(amountNum) && amountNum >= 0;
};

// Sanitization functions
export const sanitizeString = (str) => {
  if (typeof str !== 'string') return '';
  return str.trim().replace(/[<>]/g, ''); // Basic XSS prevention
};

export const sanitizePhone = (phone) => {
  if (typeof phone !== 'string') return '';
  return phone.replace(/[^\d\+\-\(\)\s]/g, '');
};

export const sanitizeNumber = (num) => {
  const parsed = parseFloat(num);
  return isNaN(parsed) ? 0 : parsed;
};

// Form validation for client-side
export const validateOrderForm = (formData) => {
  const errors = {};

  // Customer name
  if (!formData.customer_name?.trim()) {
    errors.customer_name = 'Customer name is required';
  } else if (formData.customer_name.length < 2) {
    errors.customer_name = 'Customer name must be at least 2 characters';
  }

  // Customer phone
  if (!formData.customer_phone?.trim()) {
    errors.customer_phone = 'Phone number is required';
  } else if (!isValidPhone(formData.customer_phone)) {
    errors.customer_phone = 'Please enter a valid phone number';
  }

  // Customer address
  if (!formData.customer_address?.trim()) {
    errors.customer_address = 'Address is required';
  } else if (formData.customer_address.length < 10) {
    errors.customer_address = 'Please provide a complete address';
  }

  // Destination city
  if (!formData.destination_city?.trim()) {
    errors.destination_city = 'Destination city is required';
  }

  // Weight
  if (!formData.weight) {
    errors.weight = 'Weight is required';
  } else if (!isValidWeight(formData.weight)) {
    errors.weight = 'Weight must be between 0.1 and 1000 kg';
  }

  // Email (optional)
  if (formData.customer_email && !isValidEmail(formData.customer_email)) {
    errors.customer_email = 'Please enter a valid email address';
  }

  // COD Amount (optional)
  if (formData.cod_amount && !isValidCODAmount(formData.cod_amount)) {
    errors.cod_amount = 'COD amount must be a valid number';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// API token validation
export const validateApiToken = (token) => {
  if (!token || typeof token !== 'string') {
    throw new ValidationError('API token is required');
  }
  
  if (token.length < 10) {
    throw new ValidationError('Invalid API token format');
  }
  
  return true;
};

// Shop URL validation
export const validateShopUrl = (shopUrl) => {
  if (!shopUrl || typeof shopUrl !== 'string') {
    throw new ValidationError('Shop URL is required');
  }
  
  const shopRegex = /^[a-zA-Z0-9\-]+\.myshopify\.com$/;
  if (!shopRegex.test(shopUrl)) {
    throw new ValidationError('Invalid Shopify shop URL format');
  }
  
  return true;
};
