// Comprehensive error handling utilities

export class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message, field = null) {
    super(message, 400, 'VALIDATION_ERROR');
    this.field = field;
  }
}

export class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTH_ERROR');
  }
}

export class ExternalAPIError extends AppError {
  constructor(message, service = 'external', originalError = null) {
    super(message, 502, 'EXTERNAL_API_ERROR');
    this.service = service;
    this.originalError = originalError;
  }
}

// Error response formatter
export const formatErrorResponse = (error, includeStack = false) => {
  const response = {
    success: false,
    error: {
      message: error.message || 'An unexpected error occurred',
      code: error.code || 'UNKNOWN_ERROR',
      statusCode: error.statusCode || 500,
    },
  };

  if (error.field) {
    response.error.field = error.field;
  }

  if (error.service) {
    response.error.service = error.service;
  }

  if (includeStack && error.stack) {
    response.error.stack = error.stack;
  }

  return response;
};

// Async error wrapper
export const asyncHandler = (fn) => {
  return async (request, ...args) => {
    try {
      return await fn(request, ...args);
    } catch (error) {
      console.error('❌ Async handler error:', error);
      
      if (error instanceof AppError) {
        return new Response(
          JSON.stringify(formatErrorResponse(error)),
          {
            status: error.statusCode,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }

      // Handle unexpected errors
      return new Response(
        JSON.stringify(formatErrorResponse(new AppError('Internal server error'))),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
  };
};

// Validation helpers
export const validateRequired = (value, fieldName) => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    throw new ValidationError(`${fieldName} is required`, fieldName);
  }
};

export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('Invalid email format', 'email');
  }
};

export const validatePhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    throw new ValidationError('Invalid phone number format', 'phone');
  }
};

export const validateWeight = (weight) => {
  const weightNum = parseFloat(weight);
  if (isNaN(weightNum) || weightNum <= 0) {
    throw new ValidationError('Weight must be a positive number', 'weight');
  }
  if (weightNum > 1000) {
    throw new ValidationError('Weight cannot exceed 1000 kg', 'weight');
  }
};

// API response helpers
export const successResponse = (data, message = 'Success') => {
  return {
    success: true,
    message,
    data,
  };
};

export const errorResponse = (message, code = 'ERROR', statusCode = 400) => {
  return new Response(
    JSON.stringify({
      success: false,
      error: {
        message,
        code,
        statusCode,
      },
    }),
    {
      status: statusCode,
      headers: { 'Content-Type': 'application/json' },
    }
  );
};

// Logging helper
export const logError = (error, context = {}) => {
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] ❌ Error:`, {
    message: error.message,
    code: error.code,
    statusCode: error.statusCode,
    stack: error.stack,
    context,
  });
};
