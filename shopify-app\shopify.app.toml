# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "your_shopify_client_id_here"
name = "logistic-app"
handle = "logistic-app-6"
application_url = "http://localhost:3000"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders, write_products, read_customers"

[auth]
redirect_urls = ["http://localhost:3000/auth/callback", "http://localhost:3000/auth/shopify/callback", "http://localhost:3000/api/auth/callback"]

[pos]
embedded = false
